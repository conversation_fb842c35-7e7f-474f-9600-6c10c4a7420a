plugins {
    id 'com.android.library'
}
android {
    namespace 'com.smarttop.library'
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 24
        targetSdkVersion 31
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.android.support:appcompat-v7:23.0.1'
    implementation 'androidx.appcompat:appcompat:1.0.0'
}

//apply from: 'https://raw.githubusercontent.com/blundell/release-android-library/master/android-release-aar.gradle'




