plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
    id 'com.sensorsdata.analytics.android'
}

android {
    namespace 'com.boxsfejn0s7.pigfin'
    //编译SDK
    compileSdkVersion 33

    defaultConfig {
        multiDexEnabled true
        //应用包名 唯一标示
        applicationId "com.hliane.lihuahui"
//        applicationId "com.jishi.kukadai"
        //最小支持SDK
        minSdkVersion 26
        //目标SDK
        targetSdkVersion 33
        //版本号
        versionCode 53
        //版本名字
        versionName "3.0.2"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        ndk {
            // 设置支持的SO库架构
            abiFilters 'arm64-v8a','armeabi-v7a'
//            abiFilters 'armeabi-v7a' //, 'x86', 'armeabi', 'x86_64', 'arm64-v8a'
        }


    }
    signingConfigs {
        release {
            storePassword 'lh123456'
            keyAlias = 'lh123456'
            keyPassword 'lh123456'
            storeFile file('../key/helian.keystore')
        }
    }
    //编译类型
    buildTypes {
        release {
            //是否混淆
            minifyEnabled true
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources true
            //混淆文件
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            // dex突破65535的限制
            multiDexEnabled true
            signingConfig signingConfigs.release
        }
        debug {
            minifyEnabled false
            // 启用资源压缩，需配合 minifyEnabled=true 使用
            shrinkResources false
            //混淆文件
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            multiDexEnabled true
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        buildConfig = true
    }
    dataBinding {
        enabled = true
    }
}

dependencies {
    api 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.30'
    implementation fileTree(include: ['*.jar',"*.aar"], dir: 'libs')
    implementation 'com.android.support.constraint:constraint-layout:1.0.2'
    //一个开源gif控件
    //    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.3'
    //刷新控件
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.0.4'
    //没有使用特殊Header，可以不加这行
    implementation 'com.scwang.smartrefresh:SmartRefreshHeader:1.0.4'
    //开源的log工具类
    implementation 'com.orhanobut:logger:1.15'
    implementation project(':mvp-baseframe')
    implementation project(':address_selector')
    implementation project(':h5-web')
    implementation project(':common-motion')
    implementation project(':common-idcard-quality')
    implementation files('libs\\DeviceId.jar')
    //添加缺失的部分javax注解
    compileOnly 'org.glassfish:javax.annotation:10.0-b28'
    implementation 'com.android.support:multidex:1.0.1'
    //百度基础定位Sdk
    //        /*一定要引入kotlin-stdlib-jdk rongsdk中有kotlin开发的代码，如果你工程中已经引入kotlin请忽略*/
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.1.0'
//    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.5'
    //    implementation rootProject.ext.dependencies["cardview-v7"]
    //        implementation rootProject.ext.dependencies["recyclerview-v7"]
    implementation rootProject.ext.dependencies["jsbridge"]
//    implementation rootProject.ext.dependencies["latest.integration"]
    implementation(rootProject.ext.dependencies["MagicIndicator"]) {
        exclude group: 'com.android.support'
    }
    implementation rootProject.ext.dependencies["appcompat-v7"]
    implementation rootProject.ext.dependencies["dagger"]
    kapt rootProject.ext.dependencies["dagger-compiler"]
    api 'com.jakewharton:butterknife:10.2.3'
    kapt 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'com.yanzhenjie:permission:2.0.0-rc11'
    //个推
//    implementation 'com.getui:sdk:********'
    //    implementation rootProject.ext.dependencies["support-v4"]
    implementation 'cn.bingoogolapple:bga-banner:2.2.5@aar'
    //Fresco，图片加载工具
    implementation 'com.facebook.fresco:fresco:1.10.0'
    implementation 'com.facebook.fresco:imagepipeline-okhttp3:1.10.0'
    implementation 'com.android.support:support-v4:28.0.0'
    implementation 'com.android.support:cardview-v7:28.0.0'
    implementation 'com.android.support:recyclerview-v7:28.0.0'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    implementation 'com.google.code.gson:gson:2.8.5'
    //处理emoji表情的库
    implementation 'com.vdurmont:emoji-java:4.0.0'
    implementation 'com.android.support:design:28.0.0'
    implementation 'com.github.li-xiaojun:XPopup:2.9.19'
    implementation 'com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.6.9'
    implementation files('libs/main-2.2.3-release.aar')
    implementation files('libs/calc.aar')
    implementation files('libs/logger-2.2.2-release.aar')
    implementation files('libs/auth_number_product-2.14.3-log-online-standard-cuum-release.aar')
    api "io.reactivex.rxjava3:rxjava:3.1.5"
    api 'io.reactivex.rxjava3:rxandroid:3.0.2'
    api "com.squareup.retrofit2:adapter-rxjava3:2.9.0"
    // 基础依赖包，必须要依赖
    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'
    // fragment快速实现（可选）
    implementation 'com.gyf.immersionbar:immersionbar-components:3.0.0'
    implementation 'com.baidu.lbsyun:BaiduMapSDK_Location:9.3.7'
    //
    implementation 'com.aliyun.ams:alicloud-android-ha-adapter:1.2.4.0-open'
    implementation 'com.aliyun.ams:alicloud-android-ha-crashreporter:2.0.0'
    // 网络请求框架：https://github.com/getActivity/EasyHttp
    implementation 'com.github.getActivity:EasyHttp:12.8'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
}
