package com.boxsfejn0s7.pigfin;


import static com.boxsfejn0s7.pigfin.base.Common.H5HOST;
import static com.boxsfejn0s7.pigfin.widget.tx_face.WBH5FaceVerifySDK.VIDEO_REQUEST;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PersistableBundle;
import android.provider.Settings;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;

import com.blankj.utilcode.util.PathUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.boxsfejn0s7.pigfin.base.ApplicationLifecycle;
import com.boxsfejn0s7.pigfin.ui.bookkeeping.BookkeepingActivity;
import com.boxsfejn0s7.pigfin.util.screenutil.CaptureScreenUtils;
import com.boxsfejn0s7.pigfin.widget.tx_face.H5FaceWebChromeClient;
import com.boxsfejn0s7.pigfin.widget.tx_face.WBH5FaceVerifySDK;
import com.hjq.http.EasyHttp;
import com.hjq.http.listener.OnDownloadListener;
import com.hjq.http.model.HttpMethod;
import com.laulee.baseframe.base.RxBaseActivity;
import com.laulee.baseframe.utils.SystemUtil;
import com.boxsfejn0s7.pigfin.app.App;
import com.boxsfejn0s7.pigfin.app.GlobalAppComponent;
import com.boxsfejn0s7.pigfin.base.Common;
import com.boxsfejn0s7.pigfin.base.params.UpdateParams;
import com.boxsfejn0s7.pigfin.constant.Constant;
import com.boxsfejn0s7.pigfin.di.component.DaggerActivityComponent;
import com.boxsfejn0s7.pigfin.di.module.ActivityModule;
import com.boxsfejn0s7.pigfin.entity.UpdateEntity;
import com.boxsfejn0s7.pigfin.fragment.MineFragment;
import com.boxsfejn0s7.pigfin.fragment.NewHomeFragment;
import com.boxsfejn0s7.pigfin.presenter.MainActivityPresenter;
import com.boxsfejn0s7.pigfin.presenter.contact.MainActivityContact;
import com.boxsfejn0s7.pigfin.ui.LoginActivity;
import com.boxsfejn0s7.pigfin.ui.WelcomePage;
import com.boxsfejn0s7.pigfin.util.LiveDataBus;
import com.boxsfejn0s7.pigfin.util.LocationUtils;
import com.boxsfejn0s7.pigfin.widget.UpdateProgressDialog;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;


public class MainActivity extends RxBaseActivity<MainActivityPresenter> implements RadioGroup.OnCheckedChangeListener, MainActivityContact.IView {
    private ArrayList<Fragment> fragmentList;
    private static final String TAG = "MainActivity";
    @BindView(R.id.flRoot)
    FrameLayout flRoot;
    @BindView(R.id.main_tab_rbtn_homepage)
    RadioButton rbBorrow;
    @BindView(R.id.main_tab_rbtn_project)
    RadioButton rbRepay;
    @BindView(R.id.main_tab_rbtn_my)
    RadioButton rbMine;
    @BindView(R.id.rg)
    RadioGroup rg;
    @BindView(R.id.activity_main)
    LinearLayout activityMain;
    @BindView(R.id.rlTitle)
    RelativeLayout rlTitle;

    @BindView(R.id.imgClose)
    ImageView imgClose;

    @BindView(R.id.tvTitle)
    TextView tvTitle;

    private long firstTime = 0;
    private Fragment mContent;
    private String UpdateUrl;
    private String mustUpdate;

    private FragmentManager fmanager;
    private FragmentTransaction transaction;
    private NewHomeFragment homeFragment;
    private NewHomeFragment repayFragment;
    private NewHomeFragment mineFragment;
    
    private static final int PERMISSION_QUEST_TRTC_CAMERA_VERIFY = 12;//trtc模式的权限申请
    private static final int PERMISSION_QUEST_OLD_CAMERA_VERIFY = 11;//录制模式的权限申请
    private boolean belowApi21;// android 5.0以下系统


    @Override
    protected void initParams() {
//        createUpdateTipDialog("1","哈哈哈");
        // com.getui.demo.DemoPushService 为第三方自定义推送服务
//        PushManager.getInstance().initialize(App.getContext(), GeTuiIntentService.class);
        fragmentList = new ArrayList<>();
        homeFragment = new NewHomeFragment();
        homeFragment.setUrl(H5HOST + "/#/pages/appCheck/home");
        fragmentList.add(homeFragment);

        repayFragment = new NewHomeFragment();
        repayFragment.setUrl(H5HOST + "/#/pages/bill/index");
        fragmentList.add(repayFragment);

        mineFragment = new NewHomeFragment();
        mineFragment.setUrl(H5HOST + "/#/pages/my/index");
        fragmentList.add(mineFragment);

        

//        fragmentList.add(new MineFragment());
        mContent = new Fragment();
        switchContent(fragmentList.get(0));
        rg.setOnCheckedChangeListener(this);
        com.boxsfejn0s7.pigfin.util.SystemUtil.getDeviceBrand();
        com.boxsfejn0s7.pigfin.util.SystemUtil.getDeviceBrand();
        com.boxsfejn0s7.pigfin.util.SystemUtil.getSystemModel();
        com.boxsfejn0s7.pigfin.util.SystemUtil.getMac();
        com.boxsfejn0s7.pigfin.util.SystemUtil.getAPPVersion(App.getContext());
        com.boxsfejn0s7.pigfin.util.SystemUtil.getSystemVersion();
        String IMEI = com.boxsfejn0s7.pigfin.util.SystemUtil.getIMEI(getApplicationContext());

        UpdateParams updateParams = new UpdateParams();
        updateParams.clientVersion = SystemUtil.getAPPVersion(MainActivity.this);
        updateParams.softType = Constant.PARAMS_SOFTTYPE;
        mPresenter.checkUpdate(updateParams);
        PermissionUtils.permission(PermissionConstants.STORAGE)
                .callback(new PermissionUtils.SimpleCallback() {
                    @Override
                    public void onGranted() {

                    }

                    @Override
                    public void onDenied() {
                        new XPopup.Builder(ActivityUtils.getTopActivity())
                                .asConfirm("系统提示", "您需要同意存储权限继续使用,是否前往设置打开", () -> PermissionUtils.launchAppDetailsSettings())
                                .show();
                    }
                })
                .rationale((activity, shouldRequest) -> shouldRequest.again(true))
                .request();
        CaptureScreenUtils.register(ActivityUtils.getTopActivity());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setStatuBar();
        super.onCreate(savedInstanceState);
        ActivityUtils.finishActivity(WelcomePage.class);
        LiveDataBus.get().with("hideTabbar").observe(this, o -> {
            if (o instanceof NewHomeFragment && !((NewHomeFragment) o).isHidden()) {
                hideTabbar();
            }
        });
        LiveDataBus.get().with("showTabbar").observe(this, o -> {
            if (o instanceof NewHomeFragment && !((NewHomeFragment) o).isHidden()) {
                showTabbar();
            }
        });
        LiveDataBus.get().with("setTabbar").observe(this, o -> checkFragment(Integer.parseInt((String) o)));
        LiveDataBus.get().with("showTitle").observe(this, o -> {
            rlTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(o.toString());
        });
        LiveDataBus.get().with("hideTitle").observe(this, o -> {
            rlTitle.setVisibility(View.GONE);
        });
        imgClose.setOnClickListener(v -> getHomeFragment().onBackPressed());
    }

    private void setStatuBar() {

        if (Build.VERSION.SDK_INT >= 23) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        if (Build.VERSION.SDK_INT >= 21 && Build.VERSION.SDK_INT < 23) {//21
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setStatusBarColor(Color.parseColor("#3574FA"));
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//                    //实现状态栏图标和文字颜色为暗色
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }

    //防止fragment重叠
    @Override
    public void onSaveInstanceState(Bundle outState, PersistableBundle outPersistentState) {
//        super.onSaveInstanceState(outState, outPersistentState);
    }

    @Override
    protected int setContentViewId() {
        return R.layout.activity_main;
    }

    @Override
    protected boolean hasToolBar() {
        return false;
    }

    @SuppressLint("ResourceAsColor")
    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.main_tab_rbtn_homepage:
                switchContent(fragmentList.get(0));
                break;
            case R.id.main_tab_rbtn_project:
                if (!SystemUtil.checkLoginState()) {
                    LoginActivity.start(MainActivity.this);
                    finish();
                } else {
                    switchContent(fragmentList.get(1));
                }

                break;
            case R.id.main_tab_rbtn_my:
                switchContent(fragmentList.get(2));
                break;
            default:
                switchContent(fragmentList.get(0));
                break;
        }
    }

    /**
     * 提供其他地方返回时，需要指定某个Fragment
     *
     * @param fragmentFlag
     */
    public void checkFragment(int fragmentFlag) {
        switchContent(fragmentList.get(fragmentFlag));
        switch (fragmentFlag) {
            case Common.FRAGMENT_BORROW:
                rbBorrow.setChecked(true);
                break;
            case Common.FRAGMENT_REPAY:
                rbRepay.setChecked(true);
                break;
            case Common.FRAGMENT_MINE:
                rbMine.setChecked(true);
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        boolean onBackPressed = true;
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        for (Fragment fragment : fragments) {
            if (!fragment.isHidden() && fragment instanceof NewHomeFragment) {
                onBackPressed = ((NewHomeFragment) fragment).onBackPressed();
            }
        }
        if (!onBackPressed) {
            return true;
        }
        long secondTime = System.currentTimeMillis();
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (secondTime - firstTime < 2000) {
                AppUtils.exitApp();
            } else {
                Toast.makeText(MainActivity.this, "再按一次退出程序", Toast.LENGTH_SHORT).show();
                firstTime = System.currentTimeMillis();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);

    }


    /**
     * 切换Fragment
     *
     * @param to
     */
    public void switchContent(Fragment to) {
        transaction = getSupportFragmentManager().beginTransaction();
        if (mContent != to) {
            // 先判断是否被add过
            if (!to.isAdded()) {
                // 隐藏当前的fragment，add下一个到Activity中
                transaction.hide(mContent).add(R.id.flRoot, to).commitAllowingStateLoss();
            } else {
                // 隐藏当前的fragment，显示下一个
                transaction.hide(mContent).show(to).commitAllowingStateLoss();
                if (to instanceof NewHomeFragment) {
                    ((NewHomeFragment) to).reload();
                }
            }
        }
        mContent = to;
    }

    public void goToHomeFragment() {
        switchContent(fragmentList.get(0));

    }

    @Override
    protected void inject() {
        DaggerActivityComponent.builder().activityModule(new ActivityModule(this))
                .appComponent(GlobalAppComponent.getAppComponent()).build().inject(this);
    }


    @Override
    public void onServerFiled(String s) {

    }

    @Override
    public void checkUpdateSuccess(UpdateEntity updateEntity) {
        if (updateEntity.getIsUpdate() == null) {
            return;
        } else {
            String isUpdate = updateEntity.getIsUpdate();
            switch (isUpdate) {
                //强更
                case "1":
                    UpdateUrl = updateEntity.getUrl();
                    mustUpdate = "1";
                    createUpdateTipDialog(mustUpdate, updateEntity.getDescription());
                    break;
                //不更新
                case "2":

                    break;
                //软更
                case "0":
                    UpdateUrl = updateEntity.getUrl();
                    mustUpdate = "0";
                    createUpdateTipDialog(mustUpdate, updateEntity.getDescription());
                    break;
            }
        }
    }

    /**
     * 更新弹窗
     */
    private void createUpdateTipDialog(final String mustUpdate, String descriable) {
        EasyHttp.download(ApplicationLifecycle.getInstance())
                .method(HttpMethod.GET)
                .file(new File(PathUtils.getExternalAppCachePath(), "cyqb.apk"))
                .url(UpdateUrl)
//                .url("https://microdown.myapp.com/ug/%E5%BA%94%E7%94%A8%E5%AE%9D.apk?comment=channelId%3D1178424%26context%3D%26scheme%3Dtmast%253A%252F%252Fencrypt%253Fencryptdata%253DDEZsYKWwShTA4kRxF1PhDgL07sQaoNA8UFB%25252FRgoJ5m%25252Fy2U74xbGPp3loDCUeJkvqGWDYG26eaIJ5hrZv9u0dboSvc%25252BiwK3F2DflMb78US%25252FjDbTp%25252BR3%25252BGCiVv1SdcIl0hTY1XEnmNheNWgSqZ3PHFA2osrHWFMOnXjxV9pns0GnUSYdr%25252F0T260WOWJq7xRIKWs9gOu%25252B%25252Bk9fT%25252BePhJGPse1Zp4Ai9ppUgsUc%25252BKZo%25252B3kXlbvoaOmj34oGMBqSYA%25252Fpzn12L%25252FzFrT4u%25252FoWhd3DBLMYWOEvcO5vuM1HHx2%25252Bf6l6g0GF7aMkV%25252F42qQqraB6nJy%25252FVd6J%25252BD1KiGDSUVmcv0Fwi8AzSHGsqkFVlG2YKYDkv7DTpVhXlxB3innImUrX2zQFj2b5HRI2FC9eaYjzvyIRC93aSh9j%25252B0pA1Ml%25252FAW8lAKyeqrZ3QpLR3aPrdY1NvgLy7HL4O8aC1dM7zjLgy%25252FBt%25252FF68oICx3m%25252FSMKRSYFe77vb3hxADSTRWYnvIf08DMv4eQ%25252FfoxCKKcPNjshsaq2V%25252BdcCPKjEh0ntPAO3E85UN9CYSB8r8buSB79mN7DjKVwWLoNHM5jnp0aGxV1UJN%25252BQNUQ%25253D%25253D%2526start_time%253D1715841628977%2526expiry_time%253D1715845228977%26t%3D1715841628000%26signature%3DjRd%252F5bdx0hBz4lkI7%252BzuMbk3ZB8%252Fu6loQ2Rfn8OsnZoVKCCfhj7NuMAaL%252B%252BrEd686DwN7upaLLvR7xcB0Dpe%252FgqfvTRNSjE7daIw1p%252Bk%252FQC8KnTla7%252FovsyA95D%252FsSTPJYezP9RuCw4Qn39p2rLkb9Gfg885S1hez5prSeXCh%252Bc%253D&sign_type=V2&realname=20240430_c3044dea9c9836ed3aa154562dda7b83_offset_26382336")
                // 设置断点续传（默认不开启）
                .resumableTransfer(true)
                .listener(new OnDownloadListener() {

                    @Override
                    public void onDownloadStart(File file) {
                        Log.i("tag", "========开始");
                    }

                    @Override
                    public void onDownloadProgressChange(File file, int progressInt) {
                    }

                    @Override
                    public void onDownloadSuccess(File file) {
                    }

                    @Override
                    public void onDownloadFail(File file, Throwable throwable) {
                        file.delete();
                    }

                    @Override
                    public void onDownloadEnd(File file) {
                        new XPopup.Builder(MainActivity.this)
                                .dismissOnBackPressed("1".equals(mustUpdate))
                                .dismissOnTouchOutside("1".equals(mustUpdate))
                                .asConfirm("提示", "系统有更新，请点击确定进行安装", new OnConfirmListener() {
                                    @Override
                                    public void onConfirm() {
                                        AppUtils.installApp(file);
                                    }
                                }).show();
                    }

                }).start();

//        UpdateDialog dialog = new UpdateDialog(MainActivity.this);
//        dialog.show();
//        if (mustUpdate.equals("1")) {
//            dialog.setCancelable(false);
//            dialog.setClickOutSideFalse();
//
//        }
//        dialog.setTitle("提示");
//        dialog.setContent(descriable);
//        dialog.setBtnSure("去更新");
//        dialog.setSureListener(new UpdateDialog.ISureListener() {
//            @Override
//            public void sure(Dialog dialog) {
//                createDownLoadDialog(mustUpdate);
//                dialog.dismiss();
//            }
//        });

    }

    /**
     * 更新进度的弹窗
     */
    private void createDownLoadDialog(String mustUpdate) {
        UpdateProgressDialog updateProgressDialog = new UpdateProgressDialog(MainActivity.this, UpdateUrl);
        updateProgressDialog.show();
        if (mustUpdate.equals("0")) {
            updateProgressDialog.setSureListener(new UpdateProgressDialog.ISureListener() {
                @Override
                public void sure(Dialog dialog) {
                    dialog.dismiss();
                }
            });
        } else if (mustUpdate.equals("1")) {
            updateProgressDialog.setCancelable(false);
            updateProgressDialog.setBtnGone();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CaptureScreenUtils.unregisterVideo();
        CaptureScreenUtils.unregisterCapture();
        LocationUtils.INSTANCE.stop();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    public void hideTabbar() {
        rg.setVisibility(View.GONE);
    }

    public void showTabbar() {
        rg.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        Log.d(TAG, "onActivityResult --------" + requestCode);
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == VIDEO_REQUEST) {//收到录制模式调用系统相机录制完成视频的结果
            Log.d(TAG, "onActivityResult recordVideo");
            if (WBH5FaceVerifySDK.getInstance().receiveH5FaceVerifyResult(requestCode, resultCode, data)) {
                return;
            }
        } else if (requestCode == PERMISSION_QUEST_TRTC_CAMERA_VERIFY) {
            Log.d(TAG, "onActivityResult camera");
            requestCameraPermission(true, belowApi21);
        } else if (requestCode == PERMISSION_QUEST_OLD_CAMERA_VERIFY) {
            Log.d(TAG, "onActivityResult cameraAndSome");
            requestCameraPermission(false, belowApi21);
        }
        if (resultCode == RESULT_OK && requestCode == Common.TX_FACE_REQUEST) {
            if (!homeFragment.isHidden()) {
                homeFragment.txFaceFinish();
            }
            if (!repayFragment.isHidden()) {
                repayFragment.txFaceFinish();
            }
            return;
        }
        for (Fragment fragment : getSupportFragmentManager().getFragments()) {
            if ((requestCode == NewHomeFragment.Companion.getPICK_CONTACT() || requestCode == H5FaceWebChromeClient.REQUEST_H5) && !fragment.isHidden() && fragment instanceof NewHomeFragment) {
                ((NewHomeFragment) fragment).onActivityResultX(requestCode, resultCode, data);
            }
        }
    }

    private NewHomeFragment getHomeFragment() {
        if (!homeFragment.isHidden()) {
            return homeFragment;
        }
        if (!repayFragment.isHidden()) {
            return repayFragment;
        }
        return homeFragment;
    }

    /**
     * 针对trtc录制模式，申请相机权限
     */
    public void requestCameraPermission(boolean trtc, boolean belowApi21) {
        this.belowApi21 = belowApi21;
        if (checkSdkPermission(android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            Log.d(TAG, "checkSelfPermission false");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {  //23+的情况
                if (ActivityCompat.shouldShowRequestPermissionRationale(this, android.Manifest.permission.CAMERA)) {
                    //用户之前拒绝过，这里返回true
                    Log.d(TAG, "shouldShowRequestPermissionRationale true");
                    if (trtc) {
                        ActivityCompat.requestPermissions(this,
                                new String[]{android.Manifest.permission.CAMERA},
                                PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        ActivityCompat.requestPermissions(this,
                                new String[]{android.Manifest.permission.CAMERA},
                                PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                    }
                } else {
                    Log.d(TAG, "shouldShowRequestPermissionRationale false");
                    if (trtc) {
                        ActivityCompat.requestPermissions(this,
                                new String[]{android.Manifest.permission.CAMERA},
                                PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        ActivityCompat.requestPermissions(this,
                                new String[]{android.Manifest.permission.CAMERA},
                                PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                    }

                }
            } else {
                if (trtc) {
                    //23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开
                    openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                } else {
                    //23以下没法系统弹窗动态申请权限，只能用户跳转设置页面，自己打开
                    openAppDetail(PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                }
            }

        } else {
            Log.d(TAG, "checkSelfPermission true");
            if (trtc) {
                getHomeFragment().getWebViewClient().enterTrtcFaceVerify();
            } else {
                getHomeFragment().getWebViewClient().enterOldModeFaceVerify(belowApi21);
            }

        }
    }

    private void openAppDetail(int requestCode) {
        showWarningDialog(requestCode);
    }

    AlertDialog dialog;

    private void showWarningDialog(final int requestCode) {
        dialog = new AlertDialog.Builder(this)
                .setTitle("权限申请提示")
                .setMessage("请前往设置->应用->权限中打开相关权限，否则功能无法正常运行！")
                .setPositiveButton("确定", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int which) {
                        // 一般情况下如果用户不授权的话，功能是无法运行的，做退出处理,合作方自己根据自身产品决定是退出还是停留
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                        dialog = null;
                        enterSettingActivity(requestCode);

                    }
                }).setNegativeButton("取消", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int which) {
                        if (dialog != null && dialog.isShowing()) {
                            dialog.dismiss();
                        }
                        dialog = null;
                        WBH5FaceVerifySDK.getInstance().resetReceive();

                    }
                }).setCancelable(false).show();
    }

    private void enterSettingActivity(int requestCode) {
        //部分插件化框架中用Activity.getPackageName拿到的不一定是宿主的包名，所以改用applicationContext获取
        String packageName = getApplicationContext().getPackageName();
        Uri uri = Uri.fromParts("package", packageName, null);
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, uri);
        ResolveInfo resolveInfo = getPackageManager().resolveActivity(intent, 0);
        if (resolveInfo != null) {
            startActivityForResult(intent, requestCode);
        }
    }


    private int checkSdkPermission(String permission) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            int permissionResult = ContextCompat.checkSelfPermission(this, permission);
            Log.d(TAG, "checkSdkPermission >=23 " + permissionResult + " permission=" + permission);
            return permissionResult;
        } else {
            int permissionResult = getPackageManager().checkPermission(permission, getPackageName());
            Log.d(TAG, "checkSdkPermission <23 =" + permissionResult + " permission=" + permission);
            return permissionResult;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions,
                                           int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case PERMISSION_QUEST_TRTC_CAMERA_VERIFY: // trtc 模式，新模式。
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        Log.d(TAG, "onRequestPermissionsResult grant");
                        getHomeFragment().getWebViewClient().enterTrtcFaceVerify();
                    } else if (grantResults[0] == PackageManager.PERMISSION_DENIED
                            && ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                        Log.d(TAG, "onRequestPermissionsResult deny");
                        openAppDetail(PERMISSION_QUEST_TRTC_CAMERA_VERIFY);
                    } else {
                        Log.d(TAG, "拒绝权限并且之前没有点击不再提醒");
                        //权限被拒绝
                        askPermissionError();
                    }
                }
                break;
            case PERMISSION_QUEST_OLD_CAMERA_VERIFY://录制模式，老模式。
                if (grantResults.length > 0) {
                    if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                        Log.d(TAG, "PERMISSION_QUEST_CAMERA_RECORD_VERIFY GRANTED ");
                        getHomeFragment().getWebViewClient().enterOldModeFaceVerify(belowApi21);
                    } else {
                        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.CAMERA) == false) {
                            Toast.makeText(this, "请前往设置->应用->权限中打开相机权限，否则功能无法正常运行", Toast.LENGTH_LONG).show();
                            //权限被拒绝
                            openAppDetail(PERMISSION_QUEST_OLD_CAMERA_VERIFY);
                        } else {
                            Log.d(TAG, "onRequestPermissionsResult  camera deny");
                            askPermissionError();
                        }
                    }
                }
                break;
        }
    }

    private void askPermissionError() {
        Toast.makeText(this, "用户拒绝了权限,5秒后按钮可再点击", Toast.LENGTH_SHORT).show();
        WBH5FaceVerifySDK.getInstance().resetReceive();
    }
}
