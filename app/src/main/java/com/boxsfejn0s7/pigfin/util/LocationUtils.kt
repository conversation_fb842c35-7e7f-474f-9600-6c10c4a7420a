package com.boxsfejn0s7.pigfin.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.blankj.utilcode.util.ActivityUtils
import android.util.Log


object LocationUtils {
    private var locationListener: LocationListener? = null
    var mLocationClient: LocationClient? = null
    var currLocation: BDLocation? = null
    var longitude: BDLocation? = null
    var latitude: BDLocation? = null
    var REPORT_INTERVAL = 3000
    // 定时器相关
    private val handler = Handler(Looper.getMainLooper())
    private var locationTimer: Runnable? = null
    private var isTimerRunning = false
    /**
     * 判断位置服务是否已开启
     *
     * @param context 上下文对象
     * @return 位置服务是否已开启
     */
    fun isLocationServiceEnabled(context: Context): Boolean {
        val locationMode: Int = Settings.Secure.getInt(
            context.getContentResolver(),
            Settings.Secure.LOCATION_MODE,
            Settings.Secure.LOCATION_MODE_OFF
        )
        return locationMode != Settings.Secure.LOCATION_MODE_OFF
    }

    fun applyLocation(success: () -> Unit, failed: () -> Unit) {
        // 判断位置是否已打开
        if (!isLocationServiceEnabled(ActivityUtils.getTopActivity())) {
            SensorsUtils.upLocationErrorInfo("定位开关没打开")
            failed()
            return
        }
        requestLocation(
            {
                success()
                if (mLocationClient == null) {
                    start()
                }
            },
            onDenied = {
                SensorsUtils.upLocationErrorInfo("定位权限被拒绝")
                failed()
            },
            "定位"
        )
    }

    @SuppressLint("MissingPermission")
    fun start() {
        LocationClient.setAgreePrivacy(true)
        mLocationClient = LocationClient(ActivityUtils.getTopActivity())
        val option = LocationClientOption()
        option.locationMode = LocationClientOption.LocationMode.Hight_Accuracy;
//可选，设置定位模式，默认高精度
//LocationMode.Hight_Accuracy：高精度；
//LocationMode. Battery_Saving：低功耗；
//LocationMode. Device_Sensors：仅使用设备；
//LocationMode.Fuzzy_Locating, 模糊定位模式；v9.2.8版本开始支持，可以降低API的调用频率，但同时也会降低定位精度；
        option.setCoorType("bd09ll");
//可选，设置返回经纬度坐标类型，默认gcj02
//gcj02：国测局坐标；
//bd09ll：百度经纬度坐标；
//bd09：百度墨卡托坐标；
//海外地区定位，无需设置坐标类型，统一返回wgs84类型坐标
//        option.setFirstLocType(FirstLocTypefirstLocType)
//可选，首次定位时可以选择定位的返回是准确性优先还是速度优先，默认为速度优先
//可以搭配setOnceLocation(Boolean isOnceLocation)单次定位接口使用，当设置为单次定位时，setFirstLocType接口中设置的类型即为单次定位使用的类型
//FirstLocType.SPEED_IN_FIRST_LOC:速度优先，首次定位时会降低定位准确性，提升定位速度；
//FirstLocType.ACCUARACY_IN_FIRST_LOC:准确性优先，首次定位时会降低速度，提升定位准确性；

        option.setScanSpan(3000);
//可选，设置发起定位请求的间隔，int类型，单位ms
//如果设置为0，则代表单次定位，即仅定位一次，默认为0
//如果设置非0，需设置1000ms以上才有效

        option.isOpenGnss = true;
//可选，设置是否使用卫星定位，默认false
//使用高精度和仅用设备两种定位模式的，参数必须设置为true

        option.isLocationNotify = true;
//可选，设置是否当卫星定位有效时按照1S/1次频率输出卫星定位结果，默认false

        option.setIgnoreKillProcess(false);
//可选，定位SDK内部是一个service，并放到了独立进程。
//设置是否在stop的时候杀死这个进程，默认（建议）不杀死，即setIgnoreKillProcess(true)

        option.SetIgnoreCacheException(false);
//可选，设置是否收集Crash信息，默认收集，即参数为false

        option.setWifiCacheTimeOut(5 * 60 * 1000);
//可选，V7.2版本新增能力
//如果设置了该接口，首次启动定位时，会先判断当前Wi-Fi是否超出有效期，若超出有效期，会先重新扫描Wi-Fi，然后定位

        option.setEnableSimulateGnss(false);
//可选，设置是否需要过滤卫星定位仿真结果，默认需要，即参数为false

        option.setNeedNewVersionRgc(true);
        option.setIsNeedAddress(true)
//可选，设置是否需要最新版本的地址信息。默认需要，即参数为true
        mLocationClient?.locOption = option
        mLocationClient?.registerLocationListener(object : BDAbstractLocationListener() {
            override fun onReceiveLocation(location: BDLocation?) {
                if (location != null && location.locType == 161) {
                    currLocation = location
                    if (location.longitude != 0.0 && location.latitude != 0.0) {
                        longitude = location
                        latitude = location
                    }
                    locationListener?.info(currLocation)
                }
//                if (location != null && location.locType == 161) {
//                    SensorsUtils.upLocation(location.longitude, location.latitude)
//                    APIManager.getInstance()
//                        .request(ApplicationLifecycle.getInstance().lifecycleScope, UserService::class.java, { userService ->
//                            userService.upLocation(location.longitude, location.latitude)
//                        }, {
////                           showToast("上报成功")
//                        }, { code, message ->
//                            SensorsUtils.upLocationErrorInfo(message)
//                        })
//                } else {
//                    SensorsUtils.upLocationErrorInfo("定位数据没有 ${location?.locType}")
//                }
            }
        })

        locationTimer = object : Runnable {
            override fun run() {
                // 这里可以做周期性处理，比如上报、刷新等
                if(mLocationClient==null){
                    stop()
                    return;
                }
                mLocationClient!!.start();
                locationListener?.info(currLocation)
                // 继续循环
                handler.postDelayed(this, REPORT_INTERVAL.toLong())
            }
        }

// 启动定时器
        handler.postDelayed(locationTimer!!, REPORT_INTERVAL.toLong())

    }

    public fun addListener(lis: LocationListener) {
        this.locationListener = lis
    }

    fun stop() {
        mLocationClient?.stop()
        mLocationClient = null
    }
}

interface LocationListener {
    fun info(currLocation: BDLocation?);
}