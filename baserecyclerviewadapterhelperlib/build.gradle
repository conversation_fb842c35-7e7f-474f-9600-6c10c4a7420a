apply plugin: 'com.android.library'
android {
    namespace "com.chad.library"
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 28
    }
    buildTypes {
        release {
            consumerProguardFiles 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }


}
dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    compileOnly 'com.android.support:recyclerview-v7:28.0.0'
    implementation 'androidx.appcompat:appcompat:1.0.0'
}
