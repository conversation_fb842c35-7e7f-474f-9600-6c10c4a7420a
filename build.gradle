// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.1'
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.3'
        classpath 'com.sensorsdata.analytics.android:android-gradle-plugin2:3.5.3'
    }
}

plugins {
    id 'com.android.application' version '8.10.1' apply false
    id 'com.android.library' version '8.10.1' apply false
    id 'org.jetbrains.kotlin.android' version '2.1.0' apply false
    id 'com.sensorsdata.analytics.android' version "4.0.1" apply false
}

apply from: "config.gradle"