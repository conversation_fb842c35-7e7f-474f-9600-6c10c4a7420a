apply plugin: 'com.android.library'

android {
    namespace 'com.niuniu.mall'
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        vectorDrawables.useSupportLibrary = true
    }
}

dependencies {
    implementation(name: 'ocr-quality-online-normal-release-2.0.1-idcard', ext: 'aar')
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    api 'com.blankj:utilcodex:1.31.1'
    //多图选择
    api 'io.github.lucksiege:pictureselector:v3.11.1'
    api 'com.github.bumptech.glide:glide:4.12.0'
    api 'com.alibaba:fastjson:1.1.46.android'
    implementation 'com.github.li-xiaojun:XPopup:2.9.19'
}