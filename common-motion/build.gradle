apply plugin: 'com.android.library'

android {
    namespace 'com.mall.common'
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        vectorDrawables.useSupportLibrary = true
    }
}

dependencies {
    api(name: 'liveness-interactive-online-cn-3.17.0-release', ext: 'aar')
    compileOnly fileTree(dir: '../common-motion/libs', include: ['liveness-interactive-online-cn-3.17.0-release.aar'])
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    api 'com.blankj:utilcodex:1.31.1'
    api 'com.alibaba:fastjson:1.1.46.android'
    implementation 'com.github.li-xiaojun:XPopup:2.9.19'
}